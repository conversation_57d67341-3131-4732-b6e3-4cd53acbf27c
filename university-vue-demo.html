<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高校域名白名单管理 - Vue组件演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        :root {
            --sidebar-bg: #2d3748;
            --sidebar-text: #a0aec0;
            --sidebar-hover-bg: #4a5568;
            --sidebar-active-bg: #4a5568;
            --sidebar-active-text: #ffffff;
            --main-bg: #f7fafc;
            --border-color: #e2e8f0;
            --primary-color: #4299e1;
            --primary-hover: #2b6cb0;
        }
        body {
            background-color: var(--main-bg);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
        }
        .sidebar { background-color: var(--sidebar-bg); color: var(--sidebar-text); }
        .sidebar-link:hover { background-color: var(--sidebar-hover-bg); color: var(--sidebar-active-text); }
        .sidebar-link.active { background-color: var(--primary-color); color: var(--sidebar-active-text); }
        .app-container { padding: 20px; }
        .head-container { margin-bottom: 20px; }
        .filter-item { margin-right: 10px; }
        .el-form-item-label { margin-right: 10px; }
    </style>
</head>
<body>
    <div id="app" class="flex h-screen bg-gray-200">
        <!-- Sidebar -->
        <div class="sidebar w-64 flex-shrink-0 flex flex-col space-y-2 p-2 hidden md:block">
            <div class="px-4 py-3 mb-2">
                <h2 class="text-2xl font-semibold text-white">TREX Admin</h2>
            </div>
            <nav class="flex-1">
                <a href="#" class="sidebar-link flex items-center py-2.5 px-4 rounded transition duration-200">
                    <span>首页</span>
                </a>
                <a href="#" class="sidebar-link flex items-center py-2.5 px-4 rounded transition duration-200">
                    <span>DREX管理</span>
                </a>
                <a href="#" class="sidebar-link active flex items-center py-2.5 px-4 rounded transition duration-200 ml-4">
                    <span>高校域名白名单</span>
                </a>
                <a href="#" class="sidebar-link flex items-center py-2.5 px-4 rounded transition duration-200 ml-4">
                    <span>项目信息配置</span>
                </a>
                <a href="#" class="sidebar-link flex items-center py-2.5 px-4 rounded transition duration-200 ml-4">
                    <span>社媒解绑</span>
                </a>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <header class="flex justify-between items-center p-4 bg-white border-b border-gray-200">
                <div>
                    <h1 class="text-xl font-semibold">高校域名白名单</h1>
                    <p class="text-sm text-gray-500">首页 / DREX管理 / 高校域名白名单</p>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm">欢迎, admin</span>
                </div>
            </header>

            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100">
                <div class="app-container">
                    <!-- 工具栏 -->
                    <div class="head-container">
                        <div class="bg-white p-4 rounded-lg shadow-sm mb-4">
                            <!-- 搜索区域 -->
                            <div class="flex items-center mb-4">
                                <label class="el-form-item-label">学校名称</label>
                                <el-input
                                    v-model="query.university"
                                    clearable
                                    placeholder="学校名称"
                                    style="width: 185px;"
                                    class="filter-item"
                                ></el-input>
                                <label class="el-form-item-label">域名</label>
                                <el-input
                                    v-model="query.domain"
                                    clearable
                                    placeholder="域名"
                                    style="width: 185px;"
                                    class="filter-item"
                                ></el-input>
                                <el-button type="primary" @click="handleQuery">查询</el-button>
                                <el-button @click="handleReset">重置</el-button>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="flex justify-between items-center">
                                <div>
                                    <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
                                    <el-button type="danger" icon="el-icon-delete" @click="handleBatchDelete">批量删除</el-button>
                                </div>
                                <div>
                                    <el-button type="success" icon="el-icon-upload" @click="showImportDialog">批量导入</el-button>
                                </div>
                            </div>
                        </div>

                        <!-- 表格 -->
                        <div class="bg-white rounded-lg shadow-sm">
                            <el-table
                                :data="tableData"
                                style="width: 100%"
                                @selection-change="handleSelectionChange"
                                v-loading="loading"
                            >
                                <el-table-column type="selection" width="55"></el-table-column>
                                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                                <el-table-column prop="university" label="学校名称"></el-table-column>
                                <el-table-column prop="domain" label="域名"></el-table-column>
                                <el-table-column prop="updateTime" label="更新时间"></el-table-column>
                                <el-table-column label="操作" width="150" align="center">
                                    <template slot-scope="scope">
                                        <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
                                        <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 分页 -->
                            <div class="p-4">
                                <el-pagination
                                    @size-change="handleSizeChange"
                                    @current-change="handleCurrentChange"
                                    :current-page="currentPage"
                                    :page-sizes="[10, 20, 50, 100]"
                                    :page-size="pageSize"
                                    layout="total, sizes, prev, pager, next, jumper"
                                    :total="total">
                                </el-pagination>
                            </div>
                        </div>
                    </div>

                    <!-- 新增/编辑对话框 -->
                    <el-dialog
                        :title="dialogTitle"
                        :visible.sync="dialogVisible"
                        width="500px"
                        :close-on-click-modal="false"
                    >
                        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
                            <el-form-item label="学校名称" prop="university">
                                <el-input v-model="form.university" style="width: 370px;"></el-input>
                            </el-form-item>
                            <el-form-item label="域名" prop="domain">
                                <el-input v-model="form.domain" placeholder="例如: pku.edu.cn" style="width: 370px;"></el-input>
                            </el-form-item>
                        </el-form>
                        <div slot="footer" class="dialog-footer">
                            <el-button @click="dialogVisible = false">取消</el-button>
                            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确认</el-button>
                        </div>
                    </el-dialog>

                    <!-- 批量导入对话框 -->
                    <el-dialog
                        title="批量导入域名"
                        :visible.sync="importDialogVisible"
                        width="600px"
                        :close-on-click-modal="false"
                    >
                        <el-form label-width="120px">
                            <el-form-item label="上传CSV文件">
                                <el-upload
                                    :limit="1"
                                    :before-upload="beforeUpload"
                                    :auto-upload="false"
                                    accept=".csv"
                                    drag
                                >
                                    <i class="el-icon-upload"></i>
                                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                    <div class="el-upload__tip" slot="tip">
                                        <p>仅支持CSV文件，文件格式要求：第一列为学校名称，第二列为域名，无表头。</p>
                                        <p>示例：北京大学,pku.edu.cn</p>
                                    </div>
                                </el-upload>
                            </el-form-item>
                        </el-form>
                        <div slot="footer" class="dialog-footer">
                            <el-button @click="importDialogVisible = false">取消</el-button>
                            <el-button type="primary" :loading="importLoading">开始导入</el-button>
                        </div>
                    </el-dialog>
                </div>
            </main>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    query: {
                        university: '',
                        domain: ''
                    },
                    tableData: [
                        { id: 1, university: '北京大学', domain: 'pku.edu.cn', updateTime: '2023-10-01 10:30:00' },
                        { id: 2, university: '清华大学', domain: 'tsinghua.edu.cn', updateTime: '2023-10-01 11:00:00' },
                        { id: 3, university: '复旦大学', domain: 'fudan.edu.cn', updateTime: '2023-10-02 14:20:15' },
                        { id: 4, university: '上海交通大学', domain: 'sjtu.edu.cn', updateTime: '2023-10-03 09:05:30' },
                        { id: 5, university: 'MIT', domain: 'mit.edu', updateTime: '2023-10-05 18:45:00' },
                        { id: 6, university: 'Stanford University', domain: 'stanford.edu', updateTime: '2023-10-05 19:00:00' }
                    ],
                    loading: false,
                    currentPage: 1,
                    pageSize: 10,
                    total: 6,
                    dialogVisible: false,
                    dialogTitle: '新增域名',
                    form: {
                        id: null,
                        university: '',
                        domain: ''
                    },
                    rules: {
                        university: [
                            { required: true, message: '学校名称不能为空', trigger: 'blur' }
                        ],
                        domain: [
                            { required: true, message: '域名不能为空', trigger: 'blur' },
                            {
                                validator: (rule, value, callback) => {
                                    if (value && !/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
                                        callback(new Error('请输入有效的域名格式'))
                                    } else {
                                        callback()
                                    }
                                },
                                trigger: 'blur'
                            }
                        ]
                    },
                    submitLoading: false,
                    importDialogVisible: false,
                    importLoading: false,
                    selectedRows: []
                }
            },
            methods: {
                handleQuery() {
                    this.$message.success('查询功能演示')
                },
                handleReset() {
                    this.query = { university: '', domain: '' }
                    this.$message.info('重置查询条件')
                },
                handleAdd() {
                    this.dialogTitle = '新增域名'
                    this.form = { id: null, university: '', domain: '' }
                    this.dialogVisible = true
                },
                handleEdit(row) {
                    this.dialogTitle = '编辑域名'
                    this.form = { ...row }
                    this.dialogVisible = true
                },
                handleDelete(row) {
                    this.$confirm('确定要删除该域名吗？', '确认删除', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.$message.success('删除成功（演示）')
                    })
                },
                handleBatchDelete() {
                    if (this.selectedRows.length === 0) {
                        this.$message.warning('请选择要删除的记录')
                        return
                    }
                    this.$confirm(`确定要删除选中的 ${this.selectedRows.length} 条记录吗？`, '确认删除', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.$message.success('批量删除成功（演示）')
                    })
                },
                handleSubmit() {
                    this.$refs.form.validate((valid) => {
                        if (valid) {
                            this.submitLoading = true

                            // 模拟后端错误处理演示
                            const university = this.form.university
                            const domain = this.form.domain

                            // 模拟重复性检查
                            const existingUniversities = ['北京大学', '清华大学', '复旦大学']
                            const existingDomains = ['pku.edu.cn', 'tsinghua.edu.cn', 'fudan.edu.cn']

                            setTimeout(() => {
                                this.submitLoading = false

                                // 模拟后端错误响应
                                if (existingUniversities.includes(university) && !this.form.id) {
                                    // 模拟axios拦截器先显示通用错误
                                    this.$message.error('学校名称已存在')
                                    // 然后显示我们的自定义错误（延迟显示）
                                    setTimeout(() => {
                                        this.$message.error(`学校名称"${university}"已存在，请使用其他名称`)
                                    }, 100)
                                    return
                                }

                                if (existingDomains.includes(domain) && !this.form.id) {
                                    // 模拟axios拦截器先显示通用错误
                                    this.$message.error('域名已存在')
                                    // 然后显示我们的自定义错误（延迟显示）
                                    setTimeout(() => {
                                        this.$message.error(`域名"${domain}"已存在，请使用其他域名`)
                                    }, 100)
                                    return
                                }

                                this.dialogVisible = false
                                this.$message.success(this.form.id ? '编辑成功（演示）' : '新增成功（演示）')
                            }, 1000)
                        }
                    })
                },
                showImportDialog() {
                    this.importDialogVisible = true
                },
                beforeUpload(file) {
                    const isCSV = file.type === 'text/csv' || file.name.endsWith('.csv')
                    if (!isCSV) {
                        this.$message.error('只能上传CSV文件!')
                        return false
                    }
                    return false
                },
                handleSelectionChange(selection) {
                    this.selectedRows = selection
                },
                handleSizeChange(val) {
                    this.pageSize = val
                },
                handleCurrentChange(val) {
                    this.currentPage = val
                }
            }
        })
    </script>
</body>
</html>
