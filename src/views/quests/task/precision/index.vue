<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="700px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="200px">
          <el-form-item label="投放id,目前写死1" prop="id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="投放渠道,twitter" prop="channel">
            <el-input v-model="form.channel" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="关联任务/活动,保留字段">
            <el-input v-model="form.task" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="所属者,edgen目前写死" prop="owner">
            <el-input v-model="form.owner" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="distribution投放金额(4小时)" prop="distributionAmount">
            <el-input v-model="form.distributionAmount" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="contribution_ai投放金额（4小时）" prop="contributionAiAmount">
            <el-input v-model="form.contributionAiAmount" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="contribution_human投放金额(1天)" prop="contributionHumanAmount">
            <el-input v-model="form.contributionHumanAmount" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="contribution_deep_human投放金额(1天)" prop="contributionDeepHumanAmount">
            <el-input v-model="form.contributionDeepHumanAmount" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="distribution用户获得的最大金额" prop="distributionMaxAmount">
            <el-input v-model="form.distributionMaxAmount" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="contribution_ai单个用户最大金额" prop="contributionAiMaxAmount">
            <el-input v-model="form.contributionAiMaxAmount" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="contribution_human单个用户最大金额(1天)" prop="contributionHumanMaxAmount">
            <el-input v-model="form.contributionHumanMaxAmount" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="contribution_deep_human单个用户最大值(1天)" prop="contributionDeepHumanMaxAmount">
            <el-input v-model="form.contributionDeepHumanMaxAmount" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="资深用户粉丝数" prop="precisionTrackCheckFollowsCount">
            <el-input v-model="form.precisionTrackCheckFollowsCount" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="ai最少打分数" prop="precisionTrackCheckAiPoint">
            <el-input v-model="form.precisionTrackCheckAiPoint" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="资深用户aura值" prop="precisionTrackCheckAsset">
            <el-input v-model="form.precisionTrackCheckAsset" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="任务投放金额" prop="engagementAmount">
            <el-input v-model="form.engagementAmount" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="任务投放担任最大金额" prop="engagementMaxAmount">
            <el-input v-model="form.engagementMaxAmount" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="投放id,目前写死1" />
        <el-table-column prop="channel" label="投放渠道,twitter" />
        <el-table-column prop="task" label="关联任务/活动,保留字段" />
        <el-table-column prop="owner" label="所属者,edgen目前写死" />
        <el-table-column prop="distributionAmount" label="distribution投放金额" />
        <el-table-column prop="contributionAiAmount" label="contribution_ai投放金额" />
        <el-table-column prop="contributionHumanAmount" label="contribution_human投放金额" />
        <el-table-column prop="contributionDeepHumanAmount" label="contribution_deep_human投放金额" />
        <el-table-column prop="distributionMaxAmount" label="distribution用户获得的最大金额" />
        <el-table-column prop="contributionAiMaxAmount" label="contribution_ai单个用户最大金额" />
        <el-table-column prop="contributionHumanMaxAmount" label="contribution_human单个用户最大金额" />
        <el-table-column prop="contributionDeepHumanMaxAmount" label="contribution_deep_human单个用户最大值" />
        <el-table-column prop="precisionTrackCheckFollowsCount" label="资深用户粉丝数" />
        <el-table-column prop="precisionTrackCheckAiPoint" label="ai最少打分数" />
        <el-table-column prop="precisionTrackCheckAsset" label="资深用户aura值" />
        <el-table-column prop="engagementAmount" label="任务投放金额" />
        <el-table-column prop="engagementMaxAmount" label="任务投放担任最大金额" />
        <el-table-column v-if="checkPer(['admin','precisionPool:edit','precisionPool:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudPrecisionPool from '@/api/quests/task/precision/precisionPool'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, channel: null, task: null, owner: null, distributionAmount: null, contributionAiAmount: null, contributionHumanAmount: null, contributionDeepHumanAmount: null, distributionMaxAmount: null, contributionAiMaxAmount: null, contributionHumanMaxAmount: null, contributionDeepHumanMaxAmount: null, precisionTrackCheckFollowsCount: null, precisionTrackCheckAiPoint: null, precisionTrackCheckAsset: null }
export default {
  name: 'PrecisionPool',
  components: { pagination, crudOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: 'aura投放奖池配置', url: 'api/precisionPool', idField: 'id', sort: 'id,desc', crudMethod: { ...crudPrecisionPool }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'precisionPool:add'],
        edit: ['admin', 'precisionPool:edit'],
        del: ['admin', 'precisionPool:del']
      },
      rules: {
        id: [
          { required: true, message: '投放id,目前写死1不能为空', trigger: 'blur' }
        ],
        channel: [
          { required: true, message: '投放渠道,twitter不能为空', trigger: 'blur' }
        ],
        owner: [
          { required: true, message: '所属者,edgen目前写死不能为空', trigger: 'blur' }
        ],
        distributionAmount: [
          { required: true, message: 'distribution投放金额不能为空', trigger: 'blur' }
        ],
        contributionAiAmount: [
          { required: true, message: 'contribution_ai投放金额不能为空', trigger: 'blur' }
        ],
        contributionHumanAmount: [
          { required: true, message: 'contribution_human投放金额不能为空', trigger: 'blur' }
        ],
        contributionDeepHumanAmount: [
          { required: true, message: 'contribution_deep_human投放金额不能为空', trigger: 'blur' }
        ],
        distributionMaxAmount: [
          { required: true, message: 'distribution用户获得的最大金额不能为空', trigger: 'blur' }
        ],
        contributionAiMaxAmount: [
          { required: true, message: 'contribution_ai单个用户最大金额不能为空', trigger: 'blur' }
        ],
        contributionHumanMaxAmount: [
          { required: true, message: 'contribution_human单个用户最大金额不能为空', trigger: 'blur' }
        ],
        contributionDeepHumanMaxAmount: [
          { required: true, message: 'contribution_deep_human单个用户最大值不能为空', trigger: 'blur' }
        ],
        precisionTrackCheckFollowsCount: [
          { required: true, message: '资深用户粉丝数不能为空', trigger: 'blur' }
        ],
        precisionTrackCheckAiPoint: [
          { required: true, message: 'ai最少打分数不能为空', trigger: 'blur' }
        ],
        precisionTrackCheckAsset: [
          { required: true, message: '资深用户aura值不能为空', trigger: 'blur' }
        ]
      }}
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
