<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">SaasId</label>
        <el-select v-model="query.saasId" clearable size="small" placeholder="SaasId" class="filter-item" style="width: 100px" @change="crud.toQuery">
          <el-option v-for="item in dict.saas_id" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>

        <label class="el-form-item-label">任务 id</label>
        <el-input v-model="query.taskId" clearable placeholder="任务id" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">事件code</label>
        <el-input v-model="query.code" clearable placeholder="事件code" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">标题</label>
        <el-input v-model="query.title" clearable placeholder="标题(模糊查询)" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />

        <el-drawer
          title="我是里面的"
          :with-header="false"
          :append-to-body="true"
          :visible.sync="innerAllDrawer"
          :direction="direction"
          :size="size"
        >
          <json-viewer
            :value="innerAllDrawerDate"
            :expand-depth="5"
            copyable
          />
        </el-drawer>

        <el-dialog title="json内容" :visible.sync="dialogFormVisible" center>
          <el-form>
            <el-form-item label="import json" :label-width="formLabelWidth">
              <el-input v-model="importJSONDate" type="textarea" :rows="25" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button :loading="loading == false" type="primary" @click="importJSON">确 定</el-button>
          </div>
        </el-dialog>
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式，slot = 'left' or 'right'-->
      <crudOperation :permission="permission">
        <el-button slot="right" :loading="loading == false" type="primary" class="filter-item" size="mini" @click="exportAllJson">导出json</el-button>
        <el-button slot="right" :loading="loading == false" type="primary" class="filter-item" size="mini" @click="dialogFormVisible = true">导入json</el-button>
      </crudOperation>
      <!--表单组件-->
      <el-dialog
        :close-on-click-modal="false"
        :before-close="crud.cancelCU"
        :visible.sync="crud.status.cu > 0"
        :title="crud.status.title"
        :title-align="'center'"
        width="900px"
      >
        <div class="wrapper">
          <div style="margin:30px" class="my-div">
            <el-collapse v-model="activeNames" accordion>
              <el-collapse-item title="基本配置" name="basic">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="90px">
                  <el-row>
                    <el-col :span="8">
                      <el-form-item label="项目id" prop="saasId">
                        <el-select v-model="form.saasId" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.saas_id"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="开始时间">
                        <el-date-picker v-model="form.startTime" type="datetime" style="width: 100%;" filterable placeholder="任务开始时间" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="结束时间">
                        <el-date-picker v-model="form.endTime" type="datetime" style="width: 100%;" filterable placeholder="任务结束时间" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="7">
                      <el-form-item label="任务事件">
                        <el-select v-model="form.code" filterable placeholder="任务事件">
                          <el-option
                            v-for="item in dict.event_code"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="11">
                      <el-form-item label="前端事件" style="font-size: 13px">
                        <el-tooltip class="item-tooltip" effect="dark" content="对应前端icon的文件名，不包含扩展名，默认同任务事件" placement="top">
                          <el-input v-model="form.showCode" filterable placeholder="前端任务事件" />
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="客户端类型">
                        <el-select v-model="form.clientType" filterable placeholder="客户端类型">
                          <el-option
                            v-for="item in dict.client_type"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8" style="width: 30%">
                      <el-form-item label="展示渠道">
                        <el-select v-model="form.channel" filterable placeholder="pc|app">
                          <el-option
                            v-for="item in dict.task_channel"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="页面位置">
                        <el-tooltip class="item-tooltip" effect="dark" content="postion, 任务显示到不同页面的标识，通常由前端决定" placement="top">
                          <el-input v-model="form.position" />
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="区域或模块">
                        <el-tooltip class="item-tooltip" effect="dark" content="domain, 任务显示到页面的模块标识，通常由前端决定" placement="top">
                          <el-select v-model="form.domain" filterable placeholder="请选择">
                            <el-option
                              v-for="item in dict.domain"
                              :key="item.id"
                              :label="item.label"
                              :value="item.value"
                            />
                          </el-select>
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="列表可见">
                        <el-select v-model="form.showList" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.boolean"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="任务排序">
                        <el-tooltip class="item-tooltip" effect="dark" content="order, 值越小越靠前" placement="top">
                          <el-input v-model="form.order" placeholder="排序值" />
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="任务上限">
                        <el-input v-model="form.limitCountNormal" filterable placeholder="任务次数上限" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="按钮显示">
                        <el-select v-model="form.btn" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.btn_show"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="进度显示">
                        <el-select v-model="form.showProgress" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.boolean"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="刷新周期">
                        <el-select v-model="form.cycle" filterable placeholder="任务刷新周期">
                          <el-option
                            v-for="item in dict.task_cycle"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-collapse-item>

              <el-collapse-item title="文案配置" name="detail">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="80px">
                  <el-row>
                    <el-col :span="8" style="width: 45%;">
                      <el-form-item label="APP标题">
                        <el-input v-model="form.titleApp" filterable placeholder="APP端标题" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8" style="width: 45%;">
                      <el-form-item label="PC标题">
                        <el-input v-model="form.titlePc" filterable placeholder="PC端任务标题" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 45%;">
                      <el-form-item label="APP描述">
                        <el-input v-model="form.titleDescAppNormal" filterable placeholder="APP端任务描述" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 45%;">
                      <el-form-item label="PC描述">
                        <el-input v-model="form.titleDescPcNormal" filterable placeholder="PC端任务描述" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 45%;">
                      <el-form-item label="角标内容">
                        <el-input v-model="form.labelName" filterable placeholder="任务角标内容" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 45%;">
                      <el-form-item label="角标颜色">
                        <el-input v-model="form.labelColor" filterable placeholder="任务角标颜色" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 45%;">
                      <el-form-item label="积分流水">
                        <el-input v-model="form.ledgerTitle" placeholder="积分流水描述" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 90%;">
                      <el-form-item label="icon">
                        <el-upload
                          v-model="form.icon"
                          :headers="headers"
                          :action="taskIconUploadApi"
                          list-type="picture-card"
                          :data="{saasId: form.saasId, code: form.showCode !== null ? form.showCode : form.code}"
                          :on-success="cropUploadSuccess"
                        >
                          <i class="el-icon-plus" />
                        </el-upload>
                        <img width="100%" :src="form.icon" alt="">
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-collapse-item>

              <el-collapse-item title="奖励配置" name="reward">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="80px">
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="发奖频率">
                        <el-tooltip class="item-tooltip" effect="dark" content="每完成n次任务，发一次奖，1表示每完成1次发奖一次" placement="top">
                          <el-input v-model="form.rewardFrequency" filterable placeholder="每完成n次任务，发一次奖" />
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="进度类型">
                        <el-tooltip class="item-tooltip" effect="dark" content="进度累加: 任务周期内，进度一直加； 进度连续：当周期内中断进度归0" placement="top">
                          <el-select v-model="form.progressType" filterable placeholder="进度计算方式">
                            <el-option
                              v-for="item in dict.progress_type"
                              :key="item.id"
                              :label="item.label"
                              :value="item.value"
                            />
                          </el-select>
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="奖励方式">
                        <el-select v-model="form.rewardForm" filterable placeholder="奖励方式">
                          <el-option
                            v-for="item in dict.reward_form"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="领取方式">
                        <el-tooltip class="item-tooltip" effect="dark" content="随机奖励暂时不支持管理端配置" placement="top">
                          <el-select v-model="form.provideType" filterable placeholder="奖励领取方式">
                            <el-option
                              v-for="item in dict.provide_type"
                              :key="item.id"
                              :label="item.label"
                              :value="item.value"
                            />
                          </el-select>
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="8">
                      <el-form-item label="奖励数量">
                        <el-input v-model="form.rewardAmount" placeholder="奖励数量" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="奖励类型">
                        <el-select v-model="form.rewardType" filterable placeholder="奖励类型">
                          <el-option
                            v-for="item in dict.reward_type"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="8">
                      <el-form-item label="展示奖励数量">
                        <el-input v-model="form.showRewardAmount" placeholder="展示奖励数量" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="奖励币种">
                        <el-input v-model="form.rewardCurrency" placeholder="默认同奖励类型" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-tooltip class="item-tooltip" effect="dark" content="多个奖励点击此按钮新增" placement="top">
                        <el-button type="primary" @click.prevent="addDomain()">增加奖励</el-button>
                      </el-tooltip>
                    </el-col>
                  </el-row>
                  <el-form-item
                    v-for="(reward, index) in form.rewards"
                    :key="reward.key"
                    :index="index"
                    :prop="'reward.' + index + '.value'"
                  >
                    <el-row>
                      <el-col :span="8">
                        <el-form-item label="奖励数量">
                          <el-input v-model="reward.rewardAmount" placeholder="奖励数量" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="奖励类型">
                          <el-select v-model="reward.rewardType" filterable placeholder="奖励内容">
                            <el-option
                              v-for="item in dict.reward_type"
                              :key="item.id"
                              :label="item.label"
                              :value="item.value"
                            />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="奖励币种">
                          <el-input v-model="reward.rewardCurrency" placeholder="默认同奖励类型" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-button type="danger" @click.prevent="removeDomain(reward)">删除</el-button>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-form>
              </el-collapse-item>

              <el-collapse-item title="X 任务配置" name="twitter">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="100px">
                  <el-row>
                    <el-col :span="8">
                      <el-form-item label="指定关注">
                        <el-input v-model="form.twitterFollow" placeholder="关注指定用户X handle" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="帖文关键词">
                        <el-input v-model="form.twitterKeyword" placeholder="发帖包含的关键字" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="用户名关键词">
                        <el-input v-model="form.twitterUsername" placeholder="twitter用户名包含的关键字" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8" style="width: 94%">
                      <el-form-item label="发帖随机文案">
                        <el-tooltip class="item-tooltip" effect="dark" content="格式需要找研发帮忙格式化" placement="top">
                          <el-input v-model="form.twitterRandomText" type="textarea" :row="7" placeholder="发帖任务随机文案" />
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="查最近帖文">
                        <el-select v-model="form.lastPost" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.boolean"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-collapse-item>

              <el-collapse-item title="DC 任务配置" name="discord">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="80px">
                  <el-row>
                    <el-col :span="12" style="width: 46%">
                      <el-form-item label="服务器id">
                        <el-input v-model="form.discordGuild" placeholder="discord服务器id" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 46.5%">
                      <el-form-item label="拥有角色">
                        <el-input v-model="form.discordGuildRole" placeholder="拥有discord某个角色id" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12" style="width: 95%">
                      <el-form-item label="授权地址">
                        <el-input v-model="form.discordAuthUrlPc" placeholder="discord pc授权地址" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12" style="width: 95%">
                      <el-form-item label="授权地址">
                        <el-input v-model="form.discordAuthUrlApp" placeholder="discord app授权地址" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-collapse-item>

              <el-collapse-item title="资产任务配置" name="wallet">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="100px">
                  <el-row>
                    <el-col :span="12" style="width: 45%;">
                      <el-form-item label="OSP回调验证">
                        <el-select v-model="form.ospCallBack" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.intboolean"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 47%;">
                      <el-form-item label="资产类型">
                        <el-select v-model="form.taskType" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.task_type"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-collapse-item>

              <el-collapse-item title="其他配置" name="other">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="98px">
                  <el-row>
                    <el-col :span="8">
                      <el-form-item label="任务详情">
                        <el-input v-model="form.url" placeholder="任务详情页链接" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-tooltip class="item-tooltip" effect="dark" content="适用于app端不使用go跳转的" placement="top">
                        <el-form-item label="APP链接文案">
                          <el-input v-model="form.linkName" placeholder="APP端任务详情文案" />
                        </el-form-item>
                      </el-tooltip>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="APP链接地址">
                        <el-input v-model="form.linkUrl" placeholder="APP端任务详情链接" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="回调注册">
                        <el-select v-model="form.callRegister" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.boolean"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="跳过验证">
                        <el-select v-model="form.skipVerification" filterable placeholder="是否跳过验证">
                          <el-option
                            v-for="item in dict.boolean"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="AppId">
                        <el-input v-model="form.appId" placeholder="appId" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="ChainId">
                        <el-input v-model="form.chainId" placeholder="chainId" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="校验任务是否执行">
                        <el-input v-model="form.checkReward" placeholder="EL表达式" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button type="default" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>

      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="saasId" label="所属项目" width="100">
          <template slot-scope="scope">
            {{ dict.label.saas_id[scope.row.saasId] }}
          </template>
        </el-table-column>
        <el-table-column prop="taskId" label="任务id" width="120" />
        <el-table-column prop="order" label="顺序(小->大)" width="100" />
        <el-table-column prop="code" label="任务事件" width="200" />
        <el-table-column prop="titleApp" label="任务标题" width="320" />
        <el-table-column prop="status" label="任务状态" width="100">
          <template slot-scope="scope">
            {{ dict.label.task_status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column prop="publishTime" label="计划发布时间" width="170" />
        <el-table-column prop="startTime" label="开始时间" width="170" />
        <el-table-column label="配置详情" style="width: 50px;" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="default" @click="taskConfigDetail(scope.row)">查看</el-button>
            <el-drawer
              title="任务配置详情"
              :with-header="false"
              :visible.sync="drawer"
              :direction="direction"
              :size="size"
              @close="closeDrawer"
            >
              <div class="custom-table">
                <el-table
                  :data="baseData"
                  border
                  style="width: 90%; margin: 18px auto;"
                  row-key="key"
                  default-expand-all="Boolean.false"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                >
                  <el-table-column prop="key" label="基本配置" />
                  <el-table-column prop="value" label="配置值" />
                </el-table>

                <el-table
                  :data="detailData"
                  border
                  style="width: 90%; margin: 15px auto;"
                  row-key="key"
                  default-expand-all="Boolean.false"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                >
                  <el-table-column prop="key" label="详细配置" />
                  <el-table-column prop="value" label="配置值" />
                </el-table>

                <el-table
                  :data="domainConfigData"
                  border
                  style="width: 90%; margin: 15px auto;"
                  row-key="key"
                  default-expand-all="Boolean.false"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                >
                  <el-table-column prop="key" label="渠道配置" />
                  <el-table-column prop="value" label="配置值" />
                </el-table>

                <el-table
                  :data="otherConfigData"
                  border
                  style="width: 90%; margin: 15px auto;"
                  row-key="key"
                  default-expand-all="false"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                >
                  <el-table-column prop="key" label="其他配置" />
                  <el-table-column prop="value" label="配置值" />
                </el-table>
              </div>
              <el-button :loading="loading == false" type="primary" style="margin-left: 16px;" @click="exportJson">
                导出json
              </el-button>
              <el-drawer
                title="我是里面的"
                :with-header="false"
                :visible.sync="innerDrawer"
                :append-to-body="true"
              >
                <json-viewer
                  :value="innerDrawerDate"
                  :expand-depth="5"
                  copyable
                />
              </el-drawer>
            </el-drawer>
          </template>
        </el-table-column>

        <el-table-column v-if="checkPer(['admin','taskConfig:edit','taskConfig:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>

        <el-table-column label="发布操作" align="center" width="170">
          <template slot-scope="scope">
            <div style="display: flex; gap: 2px; margin-right: 3px;">
              <el-dropdown split-button type="primary" size="mini" @command="handlePublishCommand" @click="releasePublish(scope.row)">
                立即发布
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="beforePublishCommand('grey', scope.row)">灰度发布</el-dropdown-item>
                  <el-dropdown-item :command="beforePublishCommand('timing', scope.row)">定时发布</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-dialog title="定时发布" :visible.sync="dialogTimingVisible" center width="25%">
                <el-form ref="timingForm" :model="timingForm">
                  <el-form-item label="发布时间" :label-width="formLabelWidth">
                    <el-date-picker
                      v-model="timingForm.time"
                      type="datetime"
                      placeholder="选择日期时间"
                    >
                      <el-input slot="prepend" v-model="timingForm.taskId" readonly />
                    </el-date-picker>
                  </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                  <el-button type="primary" @click="timingPublish">确 定</el-button>
                </div>
              </el-dialog>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTaskConfig, { exportAllJson, exportJson, greyPublish, importJSON, releasePublish, timingPublish } from '@/api/quests/task/config/taskConfig'

import { getToken } from '@/utils/auth'
import CRUD, { crud, form, header, presenter } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import rrOperation from '@crud/RR.operation'
import udOperation from '@crud/UD.operation'
import JsonViewer from 'vue-json-viewer'
import { mapGetters } from 'vuex'

const defaultForm = { showRewardAmount: null, lastPost: null, discordAuthUrlPc: null, discordAuthUrlApp: null, chainId: null, appId: null, id: null, saasId: null, taskId: null, groupId: null, isGroup: null, showList: null, ledgerTitle: null, title: null, titleApp: null, titlePc: null, titleDescAppNormal: null, titleDescAppL1: null, titleDescPcNormal: null, titleDescPcL1: null, labelName: null, labelColor: null, status: null, startTime: null, endTime: null, code: null, showCode: null, limitCountNormal: null, limitCountL1: null, taskListImage: null, taskDetailImage: null, rewardFrequency: null, cycle: null, progressType: null, rewardForm: null, provideType: null, rewardType: null, rewardAmount: null, rewardCurrency: null, rewardVipLevel: null, rewardIndex: null, order: null, domain: null, twitterFollow: null, twitterKeyword: null, twitterRandomAppendText: null, twitterRandomText: null, twitterKols: null, twitterUsername: null, discordGuild: null, discordGuildRole: null, vipLevel: null, btn: null, showProgress: null, url: null, linkName: null, linkUrl: null, channel: null, position: null, callRegister: null, taskStatusCondition: null, skipVerification: null, clientType: null, checkReward: null, rewards: [] }
export default {
  name: 'TaskConfig',
  components: { pagination, crudOperation, rrOperation, udOperation, JsonViewer },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['intboolean', 'task_type', 'saas_id', 'boolean', 'task_status', 'task_cycle', 'progress_type', 'reward_form', 'provide_type', 'reward_type', 'btn_show', 'task_channel', 'task_status_condition', 'client_type', 'domain', 'event_code'],
  cruds() {
    return CRUD({ title: '任务配置接口', url: 'api/taskConfig', idField: 'id', sort: 'saasId,domain,order', crudMethod: { ...crudTaskConfig }})
  },
  data() {
    return {
      headers: {
        'Authorization': getToken()
      },
      formLabelWidth: 20,
      dialogTimingVisible: false,
      timingForm: {
        time: null,
        taskId: null
      },
      loading: true,
      importJSONDate: null,
      dialogFormVisible: false,
      taskId: null,
      innerDrawer: false,
      innerDrawerDate: null,
      innerAllDrawer: false,
      innerAllDrawerDate: null,
      dialogVisible: false,
      show: false,
      activeNames: ['basic'], // 默认展开的项
      drawer: false,
      size: '45%',
      direction: 'rtl',
      baseData: [],
      detailData: [],
      domainConfigData: [],
      otherConfigData: [],
      selectedFormulae: 'formulae_redio_1',
      computeFormula: '',
      formulaeMap: {
        formulae_1: '',
        formulae_2: "#map['param_a'] * param_b + param_c",
        formulae_3: "#map['param_a'] / param_b + param_c",
        formulae_4: "#map['param_a'] * param_b > param_c ? param_d : param_e"
      },
      formulaeParams: {
        param_a: '',
        param_b: '',
        param_c: '',
        param_d: '',
        param_e: ''
      },
      permission: {
        add: ['admin', 'taskConfig:add'],
        edit: ['admin', 'taskConfig:edit'],
        del: ['admin', 'taskConfig:del']
      },
      rules: {
        saasId: [
          { required: true, message: 'SaasId 不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'saasId', display_name: 'SaasId' },
        { key: 'taskId', display_name: '任务 id' },
        { key: 'groupId', display_name: '分组 id' },
        { key: 'title', display_name: '任务标题' },
        { key: 'status', display_name: '任务状态' }
      ]
    }
  },
  methods: {
    beforePublishCommand(action, value) {
      return {
        'action': action,
        'value': value
      }
    },
    removeDomain(item) {
      var index = this.form.rewards.indexOf(item)
      console.info(index)
      if (index !== -1) {
        this.form.rewards.splice(index, 1)
      }
    },
    addDomain() {
      this.form.rewards.push({
        value: '',
        key: Date.now()
      })
    },
    // 任务配置详情
    taskConfigDetail(row) {
      this.drawer = true
      this.taskId = row.taskId
      this.baseData = [
        { key: 'SaasId', value: row.saasId },
        { key: '任务 id', value: row.taskId },
        { key: '分组 id', value: row.groupId },
        { key: '是否组任务', value: row.isGroup },
        { key: '任务状态', value: row.status },
        { key: '所属模块', value: row.domain },
        { key: '任务标题', value: '',
          children: [
            { key: 'APP 端任务标题', value: row.titleApp },
            { key: 'PC 端任务标题', value: row.titlePc }]
        }
      ]
      this.detailData = [
        { key: '列表展示', value: row.showList },
        { key: '任务排序', value: row.order },
        { key: '任务要求 vip 等级', value: row.vipLevel },
        { key: '积分流水描述', value: row.ledgerTitle },
        { key: '任务事件', value: row.code },
        { key: '显示事件', value: row.showCode },
        { key: '开始时间', value: row.startTime },
        { key: '结束时间', value: row.endTime },
        { key: '任务描述', value: '',
          children: [
            { key: '任务描述 (APP 非会员)', value: row.titleDescAppNormal },
            { key: '任务描述 (APP 会员 L1)', value: row.titleDescAppL1 },
            { key: '任务描述 (PC 非会员)', value: row.titleDescPcNormal },
            { key: '任务描述 (PC 会员 L1)', value: row.titleDescPcL1 }]
        },
        { key: '角标信息', value: '',
          children: [
            { key: '角标内容', value: row.labelName },
            { key: '角标颜色', value: row.labelColor }
          ]
        },
        { key: '任务次数', value: '',
          children: [
            { key: '任务次数上限 (非会员)', value: row.limitCountNormal },
            { key: '任务次数上限（会员 L1)', value: row.limitCountL1 }]
        },
        { key: '图片信息', value: '',
          children: [
            { key: '列表图片', value: row.taskListImage },
            { key: '详情图片', value: row.taskDetailImage }]
        },
        { key: '奖励配置', value: '',
          children: [
            { key: '发奖频率', value: row.rewardFrequency },
            { key: '刷新周期', value: row.cycle },
            { key: '奖励进度计算', value: row.progressType },
            { key: '奖品类型', value: row.rewardForm },
            { key: '奖励数量', value: row.rewardAmount },
            { key: '奖励币种', value: row.rewardCurrency },
            { key: '展示奖励数量', value: row.showRewardAmount }
          ]
        }
      ]
      this.domainConfigData = [
        { key: 'Twitter任务配置', value: '',
          children: [
            { key: '关注用户', value: row.twitterFollow },
            { key: '帖文关键字', value: row.twitterKeyword },
            { key: '帖文追加文案', value: row.twitterRandomAppendText },
            { key: '帖文替换文案', value: row.twitterRandomText },
            { key: '回复指定用户', value: row.twitterKols },
            { key: '用户名关键词', value: row.twitterUsername },
            { key: '查最近帖文', value: row.lastPost }
          ]
        },
        { key: 'Discord任务配置', value: '',
          children: [
            { key: 'discord 服务器 id', value: row.discordGuild },
            { key: 'discord 角色', value: row.discordGuildRole }]
        },
        { key: '资产任务配置', value: '',
          children: [
            { key: 'osp回调验证', value: row.ospCallBack },
            { key: '资产类型', value: row.taskType }]
        }
      ]
      this.otherConfigData = [
        { key: '前端显示按钮', value: row.btn },
        { key: '显示任务进度条', value: row.showProgress },
        { key: '任务详情页', value: row.url },
        { key: '任务描述按钮文字', value: row.linkName },
        { key: '任务描述按钮链接', value: row.linkUrl },
        { key: '任务所属渠道', value: row.channel },
        { key: '页面显示位置', value: row.position },
        { key: '回调注册任务', value: row.callRegister },
        { key: '任务状态判断', value: row.taskStatusCondition },
        { key: '是否跳过验证', value: row.skipVerification },
        { key: '客户端类型', value: row.clientType },
        { key: '校验任务是否执行', value: row.checkReward }
      ]
    },
    releasePublish(row) {
      console.info(row)
      this.$confirm(`确定立即发布任务 ${row.taskId} 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        releasePublish(row).then(result => {
          this.crud.notify(result, 'success')
          // 刷新当前页面，保留搜索条件
          this.crud.refresh()
        })
      })
    },
    handlePublishCommand(row) {
      if (row.action === 'grey') {
        this.$confirm(`确定灰度发布任务 ${row.value.taskId} 吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          greyPublish(row.value).then(result => {
            this.crud.notify(result, 'success')
            // 刷新当前页面，保留搜索条件
            this.crud.refresh()
          })
        })
      } else if (row.action === 'timing') {
        this.timingForm.taskId = row.value.taskId
        this.dialogTimingVisible = true
      }
    },
    timingPublish() {
      console.info(this.timingForm)
      timingPublish(this.timingForm).then(result => {
        this.crud.notify(result, 'success')
        this.dialogTimingVisible = false
        // 刷新当前页面，保留搜索条件
        this.crud.refresh()
      })
    },
    handleChange() {
      let expression = this.formulaeMap[this.selectedFormulae.replace('_redio', '')]
      expression = expression.replace(/param_a/g, this.formulaeParams.param_a)
      expression = expression.replace(/param_b/g, this.formulaeParams.param_b)
      expression = expression.replace(/param_c/g, this.formulaeParams.param_c)
      expression = expression.replace(/param_d/g, this.formulaeParams.param_d)
      expression = expression.replace(/param_e/g, this.formulaeParams.param_e)
      if (expression.length === 0) {
        expression = this.formulaeParams.param_a
      }
      form.rewardAmount = expression
      this.form.rewardAmount = form.rewardAmount
      this.computeFormula = form.rewardAmount
    },
    cropUploadSuccess(response, file, fileList) {
      console.info(response)
      this.form.icon = file
      this.dialogVisible = true
    },
    toggleShow() {
      this.show = !this.show
    },
    closeDrawer() {
      this.drawer = false
      // 清空抽屉数据
      this.tableData = []
    },
    evaluateFormula(key) {
      // 获取 formulaeMap 中的表达式
      return this.formulaeMap[key]
    },
    exportJson() {
      this.loading = false
      exportJson(this.taskId).then(result => {
        this.innerDrawer = true
        this.innerDrawerDate = result
        this.loading = true
      })
    },
    exportAllJson() {
      const taskIds = []
      this.crud.selections.forEach(function(element) {
        taskIds.push(element.taskId)
      })
      if (taskIds.length === 0) {
        this.$message({
          message: '请选择要导出的任务',
          type: 'warning'
        })
        return
      }
      this.loading = false
      console.info(taskIds)
      exportAllJson(taskIds).then(result => {
        this.innerAllDrawer = true
        this.innerAllDrawerDate = result
        this.loading = true
      })
    },
    importJSON() {
      this.loading = false
      console.info(this.importJSONDate)
      importJSON(this.importJSONDate).then(result => {
        this.dialogFormVisible = false
        this.crud.resetQuery()
        this.loading = true
        this.importJSONDate = null
      })
    },
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  },
  // eslint-disable-next-line vue/order-in-components
  computed: {
    ...mapGetters([
      'taskIconUploadApi'
    ])
  }
}
</script>

<style scoped>
.custom-table .el-table {
  border: 1px solid #e0e0e0; /* 设置表格整体边框 */
  border-spacing: 0; /* 控制单元格之间的间距 */
}

.el-table th .cell {
  font-weight: bold; /* 加粗列头文本 */
}

.custom-table .el-table th,
.custom-table .el-table td {
  padding: 10px 10px; /* 调整单元格内边距 */
  border: 1px solid #e0e0e0; /* 设置单元格边框 */
}

.custom-table .el-table th {
  background-color: #f0f0f0; /* 设置表头背景色 */
  font-weight: bold; /* 表头文字加粗 */
}

.el-row .el-col {
  width: 30%;
  margin-left:20px;
}

</style>
