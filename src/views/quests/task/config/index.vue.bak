<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">SaasId</label>
        <el-select v-model="query.saasId" clearable size="small" placeholder="SaasId" class="filter-item" style="width: 100px" @change="crud.toQuery">
          <el-option v-for="item in dict.saas_id" :key="item.id" :label="item.label" :value="item.value" />
        </el-select>
        <label class="el-form-item-label">任务 id</label>
        <el-input v-model="query.taskId" clearable placeholder="任务id" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">分组 id</label>
        <el-input v-model="query.groupId" clearable placeholder="分组id" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">标题</label>
        <el-input v-model="query.title" clearable placeholder="标题(模糊查询)" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />

        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式，slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog
        :close-on-click-modal="false"
        :before-close="crud.cancelCU"
        :visible.sync="crud.status.cu > 0"
        :title="crud.status.title"
        :title-align="'center'"
        width="900px"
      >
        <div class="wrapper">
          <div style="margin:30px" class="my-div">
            <el-collapse v-model="activeNames" accordion>
              <el-collapse-item title="基本配置" name="basic">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="90px">
                  <el-row>
                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="SaasId" prop="saasId">
                        <el-select v-model="form.saasId" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.saas_id"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="所属模块">
                        <el-select v-model="form.domain" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.domain"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="任务APP标题(默认)">
                        <el-input v-model="form.titleApp" filterable placeholder="APP端任务标题" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="任务PC标题">
                        <el-input v-model="form.titlePc" filterable placeholder="PC 端任务标题" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-collapse-item>

              <el-collapse-item title="详细配置" name="detail">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="80px">
                  <el-row>
                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="列表展示">
                        <el-select v-model="form.showList" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.boolean"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="任务排序">
                        <el-input v-model="form.order" placeholder="任务排序" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="会员等级">
                        <el-input v-model="form.vipLevel" placeholder="任务要求的vip等级 +0: 普通用户以上，1: 会员专享" />
                      </el-form-item>
                    </el-col>

                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="积分流水">
                        <el-input v-model="form.ledgerTitle" placeholder="积分流水描述" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="开始时间">
                        <el-date-picker v-model="form.startTime" type="datetime" style="width: 100%;" filterable placeholder="任务开始时间" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="结束时间">
                        <el-date-picker v-model="form.endTime" type="datetime" style="width: 100%;" filterable placeholder="任务结束时间" />
                      </el-form-item>
                    </el-col>

                    <el-col :span="12" style="width: 95%;">
                      <el-form-item label="任务描述">
                        <el-input v-model="form.titleDescAppNormal" filterable placeholder="任务描述(APP非会员)" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 95%;">
                      <el-form-item label="任务描述">
                        <el-input v-model="form.titleDescAppL1" filterable placeholder="任务描述(APP会员L1)" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 95%;">
                      <el-form-item label="任务描述">
                        <el-input v-model="form.titleDescPcNormal" filterable placeholder="任务描述(PC非会员)" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 95%;">
                      <el-form-item label="任务描述">
                        <el-input v-model="form.titleDescPcL1" filterable placeholder="任务描述(PC会员L1)" />
                      </el-form-item>
                    </el-col>

                    <el-col :span="12" style="width: 46%;">
                      <el-form-item label="角标内容">
                        <el-input v-model="form.labelName" filterable placeholder="任务角标内容" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 46.5%;">
                      <el-form-item label="角标颜色">
                        <el-input v-model="form.labelColor" filterable placeholder="任务角标颜色" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 46%;">
                      <el-form-item label="任务事件">
                        <el-input v-model="form.code" filterable placeholder="任务事件编号" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 46.5%;">
                      <el-form-item label="显示事件">
                        <el-input v-model="form.showCode" filterable placeholder="前端需要显示的事件" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 46%;">
                      <el-form-item label="任务上限">
                        <el-input v-model="form.limitCountNormal" filterable placeholder="任务次数上限(非会员)" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 46.5%;">
                      <el-form-item label="任务上限">
                        <el-input v-model="form.limitCountL1" filterable placeholder="任务次数上限(会员L1)" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 46.5%;">
                      <div class="el-upload">
                        <img :src="form.taskListImage" title="点击上传头像" class="avatar" @click="toggleShow">
                        <myUpload
                          v-model="show"
                          :headers="headers"
                          :url="updateAvatarApi"
                          :params="{taskId: form.taskId, code: form.showCode ? form.showCode : form.code}"
                          @crop-upload-success="cropUploadSuccess"
                        />
                      </div>
                    </el-col>
                  </el-row>
                </el-form>
              </el-collapse-item>

              <el-collapse-item title="奖励配置" name="reward">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="80px">
                  <el-row>
                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="发奖频率">
                        <el-input v-model="form.rewardFrequency" filterable placeholder="每完成n次任务，发一次奖" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="刷新周期">
                        <el-select v-model="form.cycle" filterable placeholder="任务刷新周期">
                          <el-option
                            v-for="item in dict.task_cycle"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="进度计算">
                        <el-select v-model="form.progressType" filterable placeholder="进度计算方式">
                          <el-option
                            v-for="item in dict.progress_type"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="奖品计算">
                        <el-select v-model="form.rewardForm" filterable placeholder="奖品计算方式">
                          <el-option
                            v-for="item in dict.reward_form"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="积分领取">
                        <el-select v-model="form.provideType" filterable placeholder="积分领取方式">
                          <el-option
                            v-for="item in dict.provide_type"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 30%;">
                      <el-form-item label="奖励类型">
                        <el-select v-model="form.rewardType" filterable placeholder="奖励类型">
                          <el-option
                            v-for="item in dict.reward_type"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="奖励币种">
                        <el-input v-model="form.rewardCurrency" placeholder="奖励币种" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="会员等级">
                        <el-input v-model="form.rewardVipLevel" placeholder="奖励专属的会员等级" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="随机奖励">
                        <el-input v-model="form.rewardIndex" placeholder="随机奖励的标号" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="选择公式">
                        <el-radio-group v-model="selectedFormulae">
                          <el-radio label="formulae_redio_1" @change="handleChange">纯数值</el-radio>
                          <el-radio label="formulae_redio_2" @change="handleChange">#map['a'] * b + c</el-radio>
                          <el-radio label="formulae_redio_3" @change="handleChange">#map['a'] / b + c</el-radio>
                          <el-radio label="formulae_redio_4" @change="handleChange">#map['a'] * b > c ? d : e</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>

                    <el-col :span="12" style="width: 80%">
                      <!-- 根据选择的表单类型动态展示不同的表单 -->
                      <el-form-item v-if="selectedFormulae === 'formulae_redio_1'" label="计算参数">
                        <el-input v-model="formulaeParams.param_a" placeholder="计算参数 a" @input="handleChange" />
                      </el-form-item>

                      <el-form-item v-if="selectedFormulae === 'formulae_redio_2' || selectedFormulae === 'formulae_redio_3'" label="计算参数">
                        <el-input v-model="formulaeParams.param_a" placeholder="计算参数 a" @input="handleChange" />
                        <el-input v-model="formulaeParams.param_b" placeholder="计算参数 b" @input="handleChange" />
                        <el-input v-model="formulaeParams.param_c" placeholder="计算参数 c" @input="handleChange" />
                      </el-form-item>

                      <el-form-item v-if="selectedFormulae === 'formulae_redio_4'" label="计算参数">
                        <el-input v-model="formulaeParams.param_a" placeholder="计算参数 a" @input="handleChange" />
                        <el-input v-model="formulaeParams.param_b" placeholder="计算参数 b" @input="handleChange" />
                        <el-input v-model="formulaeParams.param_c" placeholder="计算参数 c" @input="handleChange" />
                        <el-input v-model="formulaeParams.param_d" placeholder="计算参数 d" @input="handleChange" />
                        <el-input v-model="formulaeParams.param_e" placeholder="计算参数 e" @input="handleChange" />
                      </el-form-item>
                    </el-col>

                    <el-col :span="12" style="width: 80%">
                      <el-form-item label="公式预览">
                        <el-input v-model="computeFormula" disabled />
                      </el-form-item>
                    </el-col>

                  </el-row>
                </el-form>
              </el-collapse-item>

              <el-collapse-item title="twitter任务配置" name="twitter">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="80px">
                  <el-row>
                    <el-col :span="8">
                      <el-form-item label="关注用户">
                        <el-input v-model="form.twitterFollow" placeholder="twitter被关注的人" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="帖文包含">
                        <el-input v-model="form.twitterKeyword" placeholder="发帖包含的关键字" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="追加文案">
                        <el-input v-model="form.twitterRandomAppendText" placeholder="贴文追加的文案" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="替换文案">
                        <el-input v-model="form.twitterRandomText" placeholder="贴文替换的文案" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="回复指定">
                        <el-input v-model="form.twitterKols" placeholder="回复任务要求指定的用户" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="用户名含">
                        <el-input v-model="form.twitterUsername" placeholder="twitter用户名包含的关键字" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-collapse-item>

              <el-collapse-item title="discord任务配置" name="discord">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="80px">
                  <el-row>
                    <el-col :span="12" style="width: 46%">
                      <el-form-item label="服务器id">
                        <el-input v-model="form.discordGuild" placeholder="discord服务器id" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" style="width: 46.5%">
                      <el-form-item label="拥有角色">
                        <el-input v-model="form.discordGuildRole" placeholder="拥有discord某个角色" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-collapse-item>

              <el-collapse-item title="其他配置" name="other">
                <el-form ref="form" label-position="left" :model="form" :rules="rules" size="small" label-width="80px">
                  <el-row>
                    <el-col :span="8">
                      <el-form-item label="前端显示按钮">
                        <el-select v-model="form.btn" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.btn_show"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="显示任务进度条">
                        <el-select v-model="form.showProgress" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.boolean"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="任务的详情页">
                        <el-input v-model="form.url" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="任务描述按钮文字">
                        <el-input v-model="form.linkName" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="任务描述按钮链接">
                        <el-input v-model="form.linkUrl" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="任务所属渠道">
                        <el-select v-model="form.channel" filterable placeholder="任务所属渠道 pc|app">
                          <el-option
                            v-for="item in dict.task_channel"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="显示页面位置">
                        <el-input v-model="form.position" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="回调注册任务">
                        <el-select v-model="form.callRegister" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.boolean"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="任务状态判断">
                        <el-select v-model="form.taskStatusCondition" filterable placeholder="请选择">
                          <el-option
                            v-for="item in dict.task_status_condition"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="是否跳过验证">
                        <el-select v-model="form.skipVerification" filterable placeholder="是否跳过验证">
                          <el-option
                            v-for="item in dict.boolean"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="客户端类型">
                        <el-select v-model="form.clientType" filterable placeholder="任务所属客户端类型">
                          <el-option
                            v-for="item in dict.client_type"
                            :key="item.id"
                            :label="item.label"
                            :value="item.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button type="default" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>

      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="saasId" label="SaasId">
          <template slot-scope="scope">
            {{ dict.label.saas_id[scope.row.saasId] }}
          </template>
        </el-table-column>
        <el-table-column prop="taskId" label="任务id" />
        <el-table-column prop="groupId" label="分组id" />
        <el-table-column prop="title" label="任务标题" />
        <el-table-column prop="status" label="任务状态">
          <template slot-scope="scope">
            {{ dict.label.task_status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" />
        <el-table-column prop="endTime" label="结束时间" />
        <el-table-column label="配置详情" style="width: 50px;" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="default" @click="taskConfigDetail(scope.row)">查看</el-button>
            <el-drawer
              title="任务配置详情"
              :with-header="false"
              :visible.sync="drawer"
              :direction="direction"
              :size="size"
              @close="closeDrawer"
            >
              <div class="custom-table">
                <el-table
                  :data="baseData"
                  border
                  style="width: 90%; margin: 18px auto;"
                  row-key="key"
                  default-expand-all="Boolean.false"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                >
                  <el-table-column prop="key" label="基本配置" />
                  <el-table-column prop="value" label="配置值" />
                </el-table>

                <el-table
                  :data="detailData"
                  border
                  style="width: 90%; margin: 15px auto;"
                  row-key="key"
                  default-expand-all="Boolean.false"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                >
                  <el-table-column prop="key" label="详细配置" />
                  <el-table-column prop="value" label="配置值" />
                </el-table>

                <el-table
                  :data="domainConfigData"
                  border
                  style="width: 90%; margin: 15px auto;"
                  row-key="key"
                  default-expand-all="Boolean.false"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                >
                  <el-table-column prop="key" label="渠道配置" />
                  <el-table-column prop="value" label="配置值" />
                </el-table>

                <el-table
                  :data="otherConfigData"
                  border
                  style="width: 90%; margin: 15px auto;"
                  row-key="key"
                  default-expand-all="Boolean.false"
                  :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                >
                  <el-table-column prop="key" label="其他配置" />
                  <el-table-column prop="value" label="配置值" />
                </el-table>
              </div>
            </el-drawer>
          </template>
        </el-table-column>

        <el-table-column v-if="checkPer(['admin','taskConfig:edit','taskConfig:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>

        <el-table-column label="发布操作" style="width: 50px;" align="center">
          <template slot-scope="scope">
            <div style="display: flex; gap: 2px; margin-right: 3px;">
              <el-button
                type="button"
                class="el-button el-button--warning el-button--mini el-popover__reference"
                @click="greyPublish(scope.row)"
              >灰度</el-button>
              <el-button
                type="button"
                class="el-button el-button--danger el-button--mini el-popover__reference"
                @click="releasePublish(scope.row)"
              >正式</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTaskConfig, { greyPublish, releasePublish } from '@/api/quests/task/config/taskConfig'
import { getToken } from '@/utils/auth'
import CRUD, { crud, form, header, presenter } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import rrOperation from '@crud/RR.operation'
import udOperation from '@crud/UD.operation'
import myUpload from 'vue-image-crop-upload'
import { mapGetters } from 'vuex'

const defaultForm = { id: null, saasId: null, taskId: null, groupId: null, isGroup: null, showList: null, ledgerTitle: null, title: null, titleApp: null, titlePc: null, titleDescAppNormal: null, titleDescAppL1: null, titleDescPcNormal: null, titleDescPcL1: null, labelName: null, labelColor: null, status: null, startTime: null, endTime: null, code: null, showCode: null, limitCountNormal: null, limitCountL1: null, taskListImage: null, taskDetailImage: null, rewardFrequency: null, cycle: null, progressType: null, rewardForm: null, provideType: null, rewardType: null, rewardAmount: null, rewardCurrency: null, rewardVipLevel: null, rewardIndex: null, order: null, domain: null, twitterFollow: null, twitterKeyword: null, twitterRandomAppendText: null, twitterRandomText: null, twitterKols: null, twitterUsername: null, discordGuild: null, discordGuildRole: null, vipLevel: null, btn: null, showProgress: null, url: null, linkName: null, linkUrl: null, channel: null, position: null, callRegister: null, taskStatusCondition: null, skipVerification: null, clientType: null }
export default {
  name: 'TaskConfig',
  components: { pagination, crudOperation, rrOperation, udOperation, myUpload },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  dicts: ['saas_id', 'boolean', 'task_status', 'task_cycle', 'progress_type', 'reward_form', 'provide_type', 'reward_type', 'btn_show', 'task_channel', 'task_status_condition', 'client_type', 'domain'],
  cruds() {
    return CRUD({ title: '任务配置接口', url: 'api/taskConfig', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTaskConfig }})
  },
  data() {
    return {
      headers: {
        'Authorization': getToken()
      },
      show: false,
      activeNames: ['basic'], // 默认展开的项
      drawer: false,
      size: '45%',
      direction: 'rtl',
      baseData: [],
      detailData: [],
      domainConfigData: [],
      otherConfigData: [],
      selectedFormulae: 'formulae_redio_1',
      computeFormula: '',
      formulaeMap: {
        formulae_1: '',
        formulae_2: "#map['param_a'] * param_b + param_c",
        formulae_3: "#map['param_a'] / param_b + param_c",
        formulae_4: "#map['param_a'] * param_b > param_c ? param_d : param_e"
      },
      formulaeParams: {
        param_a: '',
        param_b: '',
        param_c: '',
        param_d: '',
        param_e: ''
      },
      permission: {
        add: ['admin', 'taskConfig:add'],
        edit: ['admin', 'taskConfig:edit'],
        del: ['admin', 'taskConfig:del']
      },
      rules: {
        saasId: [
          { required: true, message: 'SaasId 不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'saasId', display_name: 'SaasId' },
        { key: 'taskId', display_name: '任务 id' },
        { key: 'groupId', display_name: '分组 id' },
        { key: 'title', display_name: '任务标题' },
        { key: 'status', display_name: '任务状态' }
      ]
    }
  },
  methods: {
    // 任务配置详情
    taskConfigDetail(row) {
      this.drawer = true
      this.baseData = [
        { key: 'SaasId', value: row.saasId },
        { key: '任务 id', value: row.taskId },
        { key: '分组 id', value: row.groupId },
        { key: '是否组任务', value: row.isGroup },
        { key: '任务状态', value: row.status },
        { key: '所属模块', value: row.domain },
        { key: '任务标题', value: '',
          children: [
            { key: 'APP 端任务标题', value: row.titleApp },
            { key: 'PC 端任务标题', value: row.titlePc }]
        }
      ]
      this.detailData = [
        { key: '列表展示', value: row.showList },
        { key: '任务排序', value: row.order },
        { key: '任务要求 vip 等级', value: row.vipLevel },
        { key: '积分流水描述', value: row.ledgerTitle },
        { key: '任务事件', value: row.code },
        { key: '显示事件', value: row.showCode },
        { key: '开始时间', value: row.startTime },
        { key: '结束时间', value: row.endTime },
        { key: '任务描述', value: '',
          children: [
            { key: '任务描述 (APP 非会员)', value: row.titleDescAppNormal },
            { key: '任务描述 (APP 会员 L1)', value: row.titleDescAppL1 },
            { key: '任务描述 (PC 非会员)', value: row.titleDescPcNormal },
            { key: '任务描述 (PC 会员 L1)', value: row.titleDescPcL1 }]
        },
        { key: '角标信息', value: '',
          children: [
            { key: '角标内容', value: row.labelName },
            { key: '角标颜色', value: row.labelColor }
          ]
        },
        { key: '任务次数', value: '',
          children: [
            { key: '任务次数上限 (非会员)', value: row.limitCountNormal },
            { key: '任务次数上限（会员 L1)', value: row.limitCountL1 }]
        },
        { key: '图片信息', value: '',
          children: [
            { key: '列表图片', value: row.taskListImage },
            { key: '详情图片', value: row.taskDetailImage }]
        },
        { key: '奖励配置', value: '',
          children: [
            { key: '发奖频率', value: row.rewardFrequency },
            { key: '刷新周期', value: row.cycle },
            { key: '奖励进度计算', value: row.progressType },
            { key: '奖品类型', value: row.rewardForm },
            { key: '奖励数量', value: row.rewardAmount },
            { key: '奖励币种', value: row.rewardCurrency },
            { key: '奖励会员等级', value: row.rewardVipLevel },
            { key: '随机奖励', value: row.rewardIndex }]
        }
      ]
      this.domainConfigData = [
        { key: 'Twitter 任务配置', value: '',
          children: [
            { key: '关注用户', value: row.twitterFollow },
            { key: '帖文关键字', value: row.twitterKeyword },
            { key: '帖文追加文案', value: row.twitterRandomAppendText },
            { key: '帖文替换文案', value: row.twitterRandomText },
            { key: '回复指定用户', value: row.twitterKols },
            { key: '用户名关键词', value: row.twitterUsername }]
        },
        { key: 'Discord 任务配置', value: '',
          children: [
            { key: 'discord 服务器 id', value: row.discordGuild },
            { key: 'discord 角色', value: row.discordGuildRole }]
        }
      ]
      this.otherConfigData = [
        { key: '前端显示按钮', value: row.btn },
        { key: '显示任务进度条', value: row.showProgress },
        { key: '任务详情页', value: row.url },
        { key: '任务描述按钮文字', value: row.linkName },
        { key: '任务描述按钮链接', value: row.linkUrl },
        { key: '任务所属渠道', value: row.channel },
        { key: '页面显示位置', value: row.position },
        { key: '回调注册任务', value: row.callRegister },
        { key: '任务状态判断', value: row.taskStatusCondition },
        { key: '是否跳过验证', value: row.skipVerification },
        { key: '客户端类型', value: row.clientType }
      ]
    },
    greyPublish(row) {
      greyPublish(row)
    },
    releasePublish(row) {
      releasePublish(row)
    },
    handleChange() {
      let expression = this.formulaeMap[this.selectedFormulae.replace('_redio', '')]
      expression = expression.replace(/param_a/g, this.formulaeParams.param_a)
      expression = expression.replace(/param_b/g, this.formulaeParams.param_b)
      expression = expression.replace(/param_c/g, this.formulaeParams.param_c)
      expression = expression.replace(/param_d/g, this.formulaeParams.param_d)
      expression = expression.replace(/param_e/g, this.formulaeParams.param_e)
      if (expression.length === 0) {
        expression = this.formulaeParams.param_a
      }
      form.rewardAmount = expression
      this.form.rewardAmount = form.rewardAmount
      this.computeFormula = form.rewardAmount
    },
    cropUploadSuccess(jsonData, field) {
      location.reload()
    },
    toggleShow() {
      this.show = !this.show
    },
    closeDrawer() {
      this.drawer = false
      // 清空抽屉数据
      this.tableData = []
    },
    evaluateFormula(key) {
      // 获取 formulaeMap 中的表达式
      return this.formulaeMap[key]
    },
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  },
  // eslint-disable-next-line vue/order-in-components
  computed: {
    ...mapGetters([
      'updateAvatarApi'
    ])
  }
}
</script>

<style scoped>
.custom-table .el-table {
  border: 1px solid #e0e0e0; /* 设置表格整体边框 */
  border-spacing: 0; /* 控制单元格之间的间距 */
}

.el-table th .cell {
  font-weight: bold; /* 加粗列头文本 */
}

.custom-table .el-table th,
.custom-table .el-table td {
  padding: 10px 10px; /* 调整单元格内边距 */
  border: 1px solid #e0e0e0; /* 设置单元格边框 */
}

.custom-table .el-table th {
  background-color: #f0f0f0; /* 设置表头背景色 */
  font-weight: bold; /* 表头文字加粗 */
}

.el-row .el-col {
  width: 30%;
  margin-left:20px;
}

</style>
