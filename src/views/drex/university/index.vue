<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">学校名称</label>
        <el-input v-model="query.university" clearable placeholder="学校名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">域名</label>
        <el-input v-model="query.domain" clearable placeholder="域名" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission">
        <template slot="right">
          <el-button
            v-permission="permission.add"
            class="filter-item"
            size="mini"
            type="success"
            icon="el-icon-upload"
            @click="showImportDialog"
          >
            批量导入
          </el-button>
        </template>
      </crudOperation>

      <!--表单组件-->
      <el-dialog
        :close-on-click-modal="false"
        :before-close="crud.cancelCU"
        :visible.sync="crud.status.cu > 0"
        :title="crud.status.title"
        width="500px"
      >
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="100px">
          <el-form-item label="学校名称" prop="university">
            <el-input v-model="form.university" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="域名" prop="domain">
            <el-input v-model="form.domain" placeholder="例如: pku.edu.cn" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>

      <!-- 批量导入对话框 -->
      <el-dialog
        title="批量导入域名"
        :visible.sync="importDialogVisible"
        width="600px"
        :close-on-click-modal="false"
      >
        <el-form ref="importForm" :model="importForm" label-width="120px">
          <el-form-item label="上传CSV文件">
            <el-upload
              ref="csvUpload"
              :limit="1"
              :before-upload="beforeUpload"
              :on-change="handleFileChange"
              :auto-upload="false"
              accept=".csv"
              drag
            >
              <i class="el-icon-upload" />
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div slot="tip" class="el-upload__tip">
                <p>仅支持CSV文件，文件格式要求：第一列为学校名称，第二列为域名，无表头。</p>
                <p>示例：北京大学,pku.edu.cn</p>
              </div>
            </el-upload>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelImport">取消</el-button>
          <el-button type="primary" :loading="importLoading" @click="handleImport">开始导入</el-button>
        </div>
      </el-dialog>

      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="university" label="学校名称" />
        <el-table-column prop="domain" label="域名" />
        <el-table-column prop="updateTime" label="更新时间" :formatter="formatTime" />
        <el-table-column v-if="checkPer(['admin','university:edit','university:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudUniversity from '@/api/drex/university/university'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, university: null, domain: null, updateTime: null }
export default {
  name: 'University',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '高校域名白名单', url: 'api/trex/university', idField: 'id', sort: 'id,desc', crudMethod: { ...crudUniversity }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'university:add'],
        edit: ['admin', 'university:edit'],
        del: ['admin', 'university:del']
      },
      rules: {
        university: [
          { required: true, message: '学校名称不能为空', trigger: 'blur' }
        ],
        domain: [
          { required: true, message: '域名不能为空', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              callback()
            },
            trigger: 'blur'
          }
        ]
      },
      importDialogVisible: false,
      importForm: {},
      importLoading: false,
      uploadFile: null
    }
  },
  created() {
    // 重写CRUD方法的add和edit，添加自定义错误处理
    const originalAdd = this.crud.crudMethod.add
    const originalEdit = this.crud.crudMethod.edit

    // 重写add方法
    this.crud.crudMethod.add = (form) => {
      return originalAdd(form).catch((error) => {
        this.handleApiError(error)
        throw error // 重新抛出错误，让CRUD框架知道操作失败
      })
    }

    // 重写edit方法
    this.crud.crudMethod.edit = (form) => {
      return originalEdit(form).catch((error) => {
        this.handleApiError(error)
        throw error // 重新抛出错误，让CRUD框架知道操作失败
      })
    }
  },
  methods: {
    formatTime(row, column, cellValue) {
      if (!cellValue) return '-'
      const date = new Date(cellValue)
      return date.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-')
    },

    // 显示导入对话框
    showImportDialog() {
      this.importDialogVisible = true
      this.uploadFile = null
    },

    // 取消导入
    cancelImport() {
      this.importDialogVisible = false
      this.uploadFile = null
      this.$refs.csvUpload.clearFiles()
    },

    // 文件上传前验证
    beforeUpload(file) {
      const isCSV = file.type === 'text/csv' || file.name.endsWith('.csv')
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isCSV) {
        this.$message.error('只能上传CSV文件!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!')
        return false
      }
      return false // 阻止自动上传
    },

    // 文件选择变化
    handleFileChange(file, fileList) {
      this.uploadFile = file.raw
    },

    // 处理导入
    handleImport() {
      if (!this.uploadFile) {
        this.$message.error('请选择要导入的CSV文件')
        return
      }

      this.importLoading = true
      crudUniversity.batchImport(this.uploadFile)
        .then(response => {
          // 构建结果消息
          let message = '<div style="margin-bottom: 10px;">导入结果统计：</div>'
          message += `<div>总记录数：${response.total} 条</div>`

          if (response.success > 0) {
            message += `<div style="color: #67C23A; margin-top: 10px;">成功导入：${response.success} 条</div>`
            message += '<div style="margin: 5px 0; color: #67C23A; font-size: 13px;">'
            response.successList.forEach(item => {
              message += `${item}<br>`
            })
            message += '</div>'
          }

          if (response.error > 0) {
            message += `<div style="color: #E6A23C; margin-top: 10px;">导入失败：${response.error} 条</div>`
            message += '<div style="margin: 5px 0; color: #E6A23C; font-size: 13px;">'
            response.errorList.forEach(item => {
              message += `${item}<br>`
            })
            message += '</div>'
          }

          if (response.skipped > 0) {
            message += `<div style="color: #909399; margin-top: 10px;">跳过记录：${response.skipped} 条</div>`
          }

          this.$msgbox({
            title: '导入完成',
            dangerouslyUseHTMLString: true,
            message: message,
            type: 'info',
            showCancelButton: false,
            confirmButtonText: '确定'
          }).then(() => {
            this.crud.refresh()
            this.cancelImport()
          })
        })
        .catch(error => {
          this.handleImportError(error)
        })
        .finally(() => {
          this.importLoading = false
        })
    },

    // 处理批量导入错误
    handleImportError(error) {
      console.error('批量导入错误:', error)

      if (error.response && error.response.data) {
        const errorData = error.response.data
        const errorCode = errorData.errorCode
        const message = errorData.message
        const data = errorData.errorData

        switch (errorCode) {
          case 'UNIVERSITY_EXISTS':
            this.$message.error(`批量导入失败：学校名称"${data}"已存在`)
            break

          case 'DOMAIN_EXISTS':
            this.$message.error(`批量导入失败：域名"${data}"已存在`)
            break

          case 'BATCH_IMPORT_PARTIAL_FAILURE':
            // 部分导入失败的情况
            this.$message.warning(`部分导入失败：${message}`)
            // 如果有成功导入的数据，刷新表格
            if (data && data.successCount > 0) {
              this.crud.refresh()
              this.cancelImport()
            }
            break

          case 'BATCH_IMPORT_VALIDATION_ERROR':
            // 数据验证错误
            this.$message.error(`数据验证失败：${message}`)
            break

          default:
            this.$message.error(`导入失败：${message || '未知错误'}`)
            break
        }
      } else {
        this.$message.error('导入失败：网络错误，请检查网络连接后重试')
      }
    },

    // 钩子：提交前验证
    [CRUD.HOOK.beforeSubmit](crud) {
      // 重复性验证已移到后端处理
      return true
    },

    // 处理API错误
    handleApiError(error) {
      console.error('API错误详情:', error)
      console.error('错误响应数据:', error.response && error.response.data)
      // 检查是否有响应数据
      if (error.response && error.response.data) {
        const errorData = error.response.data
        const errorCode = errorData.errorCode
        const message = errorData.message
        const data = errorData.errorData

        console.log('解析的错误信息:', { errorCode, message, data })

        // 根据错误码显示特定的错误信息
        switch (errorCode) {
          case 'UNIVERSITY_EXISTS':
            // 延迟显示，避免被axios拦截器覆盖
            setTimeout(() => {
              this.$message.error(`学校名称"${data}"已存在，请使用其他名称`)
            }, 100)
            // 高亮显示学校名称字段的错误
            this.$nextTick(() => {
              if (this.$refs.form) {
                this.$refs.form.validateField('university')
              }
            })
            break

          case 'DOMAIN_EXISTS':
            // 延迟显示，避免被axios拦截器覆盖
            setTimeout(() => {
              this.$message.error(`域名"${data}"已存在，请使用其他域名`)
            }, 100)
            // 高亮显示域名字段的错误
            this.$nextTick(() => {
              if (this.$refs.form) {
                this.$refs.form.validateField('domain')
              }
            })
            break

          default:
            // 显示通用错误信息
            setTimeout(() => {
              this.$message.error(message || '操作失败，请稍后重试')
            }, 100)
            break
        }
      } else {
        // 网络错误或其他错误
        setTimeout(() => {
          this.$message.error('网络错误，请检查网络连接后重试')
        }, 100)
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
