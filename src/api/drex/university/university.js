import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/trex/university',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/trex/university',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/trex/university',
    method: 'put',
    data
  })
}

export function get(params) {
  return request({
    url: 'api/trex/university',
    method: 'get',
    params
  })
}

// 批量导入
export function batchImport(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: 'api/trex/university/batch-import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出
export function exportUniversity(params) {
  return request({
    url: 'api/trex/university/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export default { add, edit, del, get, batchImport, exportUniversity }
